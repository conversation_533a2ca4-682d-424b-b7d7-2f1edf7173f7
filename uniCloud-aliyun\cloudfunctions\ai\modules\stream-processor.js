/**
 * 流式响应处理器模块
 * 处理AI模型流式响应的核心逻辑
 */

const { SSE_MESSAGE_TYPES, createSSEMessage } = require('./config.js')
const { executeToolCall, continueConversationWithToolResults } = require('./stream-handler.js')

/**
 * 流式工具调用处理（豆包模型优化版）
 * @param {object} streamResponse - 流式响应对象
 * @param {object} sseChannel - SSE通道
 * @param {string} sessionId - 会话ID
 * @param {Array} originalMessages - 原始消息数组
 * @param {object} options - 选项配置
 */
async function handleStreamResponse(streamResponse, sseChannel, sessionId, originalMessages, options = {}) {
  let pendingToolCalls = [] // 使用数组存储工具调用，支持索引
  let assistantMessage = ''
  let hasToolCalls = false
  let firstChunkLogged = false

  for await (const chunk of streamResponse) {
    // 协作式取消：仅读取内存标志，避免每个 token 查库
    if (options && typeof options.isCanceled === 'function' && options.isCanceled()) {
      try {
        if (options && typeof options.onCanceled === 'function') {
          options.onCanceled()
        }
      } catch (_) {}
      await sseChannel.end(
        createSSEMessage(SSE_MESSAGE_TYPES.SESSION_END, sessionId, {
          message: '用户已取消',
          reason: 'canceled',
        })
      )
      return
    }
    if (!firstChunkLogged) {
      firstChunkLogged = true
    }
    const delta = chunk.choices[0]?.delta
    const finishReason = chunk.choices[0]?.finish_reason

    // 处理普通文本内容
    if (delta?.content) {
      assistantMessage += delta.content
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
          content: delta.content,
          isComplete: false,
        })
      )
    }

    // 处理工具调用 - 豆包模型增量式处理
    if (delta?.tool_calls) {
      hasToolCalls = true

      for (const toolCallDelta of delta.tool_calls) {
        const index = toolCallDelta.index || 0
        const toolCallId = toolCallDelta.id
        const name = toolCallDelta.function?.name
        const argsChunk = toolCallDelta.function?.arguments

        // 初始化工具调用对象
        if (!pendingToolCalls[index]) {
          pendingToolCalls[index] = {
            id: toolCallId || `call_${Date.now()}_${index}`,
            type: 'function',
            function: {
              name: toolCallDelta.function?.name || '',
              arguments: toolCallDelta.function?.arguments || '',
            },
          }

          // 推送工具调用开始消息
          if (toolCallDelta.function?.name) {
            await sseChannel.write(
              createSSEMessage(SSE_MESSAGE_TYPES.TOOL_CALL_START, sessionId, {
                toolName: toolCallDelta.function.name,
                toolCallId: pendingToolCalls[index].id,
              })
            )
          }
        } else {
          // 累积工具调用参数
          if (toolCallDelta.function?.name) {
            pendingToolCalls[index].function.name = toolCallDelta.function.name
          }
          if (toolCallDelta.function?.arguments) {
            pendingToolCalls[index].function.arguments += toolCallDelta.function.arguments
          }
        }
      }
    }

    // 检查是否完成工具调用
    if (finishReason === 'tool_calls' && hasToolCalls) {
      // 执行所有完整的工具调用
      const toolResults = []

      const completeToolCalls = pendingToolCalls.filter((tc) => tc && tc.function.name)

      for (const toolCall of completeToolCalls) {
        try {
          const result = await executeToolCall(toolCall, sseChannel, sessionId)
          toolResults.push({
            toolCall: toolCall,
            result: result,
          })
        } catch (error) {
          // 工具调用执行失败，错误已在 executeToolCall 中记录
          await sseChannel.write(
            createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_ERROR, sessionId, {
              toolName: toolCall.function.name,
              error: error.message,
            })
          )

          // 即使失败也要记录，以便后续处理
          toolResults.push({
            toolCall: toolCall,
            result: { error: error.message, success: false },
          })
        }
      }

      // 继续对话，让模型基于工具结果生成最终回复
      if (toolResults.length > 0) {
        await continueConversationWithToolResults(
          originalMessages,
          pendingToolCalls.filter((tc) => tc),
          toolResults,
          sseChannel,
          sessionId,
          options
        )
      }
    }

    // 处理普通对话结束
    if (finishReason === 'stop' && !hasToolCalls) {
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
          content: '',
          isComplete: true,
        })
      )
    }
  }
}

module.exports = {
  handleStreamResponse,
}

/**
 * debugToolCall 测试脚本示例
 * 演示如何使用调试函数测试各种工具调用
 */

// 测试配置
const TEST_CONFIG = {
  cloudFunctionName: 'ai',
  testCases: [
    {
      name: '获取任务列表',
      toolName: 'getTasks',
      parameters: {
        mode: 'all',
        limit: 10,
        completed: false
      }
    },
    {
      name: '创建测试任务',
      toolName: 'createTask',
      parameters: {
        title: '调试测试任务',
        content: '这是通过debugToolCall创建的测试任务',
        priority: 1,
        projectName: '测试项目'
      }
    },
    {
      name: '获取项目列表',
      toolName: 'getProjects',
      parameters: {}
    },
    {
      name: '获取标签列表',
      toolName: 'getTags',
      parameters: {}
    },
    {
      name: '获取当前时间信息',
      toolName: 'getCurrentTimeInfo',
      parameters: {}
    }
  ],
  errorTestCases: [
    {
      name: '缺少工具名称',
      toolName: '',
      parameters: {}
    },
    {
      name: '无效的参数类型',
      toolName: 'getTasks',
      parameters: 'invalid_parameters'
    },
    {
      name: '创建任务缺少标题',
      toolName: 'createTask',
      parameters: {
        content: '没有标题的任务'
      }
    }
  ]
}

/**
 * 执行单个测试用例
 * @param {object} testCase - 测试用例
 * @returns {Promise<object>} 测试结果
 */
async function runSingleTest(testCase) {
  console.log(`\n🧪 开始测试: ${testCase.name}`)
  console.log(`工具名称: ${testCase.toolName}`)
  console.log(`参数:`, JSON.stringify(testCase.parameters, null, 2))
  
  const startTime = Date.now()
  
  try {
    const result = await uniCloud.callFunction({
      name: TEST_CONFIG.cloudFunctionName,
      data: {
        action: 'debugToolCall',
        toolName: testCase.toolName,
        parameters: testCase.parameters
      }
    })
    
    const endTime = Date.now()
    const totalTime = endTime - startTime
    
    if (result.result.errCode === 0) {
      console.log(`✅ 测试成功`)
      console.log(`执行时间: ${result.result.data.executionTime}ms (总耗时: ${totalTime}ms)`)
      console.log(`会话ID: ${result.result.data.sessionId}`)
      
      // 显示数据摘要
      if (result.result.data.result && result.result.data.result.data) {
        const data = result.result.data.result.data
        if (Array.isArray(data)) {
          console.log(`返回数据: ${data.length} 条记录`)
        } else {
          console.log(`返回数据类型: ${typeof data}`)
        }
      }
    } else {
      console.log(`❌ 测试失败`)
      console.log(`错误码: ${result.result.errCode}`)
      console.log(`错误信息: ${result.result.errMsg}`)
      if (result.result.data && result.result.data.error) {
        console.log(`详细错误: ${result.result.data.error}`)
      }
    }
    
    return {
      testCase: testCase.name,
      success: result.result.errCode === 0,
      executionTime: result.result.data?.executionTime || 0,
      totalTime,
      error: result.result.errCode !== 0 ? result.result.errMsg : null
    }
    
  } catch (error) {
    console.log(`💥 测试异常: ${error.message}`)
    return {
      testCase: testCase.name,
      success: false,
      executionTime: 0,
      totalTime: Date.now() - startTime,
      error: error.message
    }
  }
}

/**
 * 执行所有测试用例
 */
async function runAllTests() {
  console.log('🚀 开始执行 debugToolCall 测试套件')
  console.log(`测试用例数量: ${TEST_CONFIG.testCases.length + TEST_CONFIG.errorTestCases.length}`)
  
  const results = []
  
  // 执行正常测试用例
  console.log('\n📋 执行正常功能测试...')
  for (const testCase of TEST_CONFIG.testCases) {
    const result = await runSingleTest(testCase)
    results.push(result)
    
    // 测试间隔，避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  // 执行错误测试用例
  console.log('\n🚨 执行错误处理测试...')
  for (const testCase of TEST_CONFIG.errorTestCases) {
    const result = await runSingleTest(testCase)
    results.push(result)
    
    // 测试间隔
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  // 生成测试报告
  generateTestReport(results)
}

/**
 * 生成测试报告
 * @param {Array} results - 测试结果数组
 */
function generateTestReport(results) {
  console.log('\n📊 测试报告')
  console.log('=' * 50)
  
  const successCount = results.filter(r => r.success).length
  const failCount = results.length - successCount
  const totalExecutionTime = results.reduce((sum, r) => sum + r.executionTime, 0)
  const avgExecutionTime = totalExecutionTime / results.length
  
  console.log(`总测试数: ${results.length}`)
  console.log(`成功: ${successCount}`)
  console.log(`失败: ${failCount}`)
  console.log(`成功率: ${((successCount / results.length) * 100).toFixed(2)}%`)
  console.log(`平均执行时间: ${avgExecutionTime.toFixed(2)}ms`)
  console.log(`总执行时间: ${totalExecutionTime}ms`)
  
  // 详细结果
  console.log('\n📝 详细结果:')
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌'
    console.log(`${index + 1}. ${status} ${result.testCase} (${result.executionTime}ms)`)
    if (!result.success && result.error) {
      console.log(`   错误: ${result.error}`)
    }
  })
  
  // 性能分析
  console.log('\n⚡ 性能分析:')
  const sortedByTime = [...results].sort((a, b) => b.executionTime - a.executionTime)
  console.log('最慢的3个测试:')
  sortedByTime.slice(0, 3).forEach((result, index) => {
    console.log(`${index + 1}. ${result.testCase}: ${result.executionTime}ms`)
  })
}

/**
 * 快速测试单个工具
 * @param {string} toolName - 工具名称
 * @param {object} parameters - 参数
 */
async function quickTest(toolName, parameters = {}) {
  console.log(`🔧 快速测试: ${toolName}`)
  
  const testCase = {
    name: `快速测试-${toolName}`,
    toolName,
    parameters
  }
  
  await runSingleTest(testCase)
}

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    runSingleTest,
    quickTest,
    TEST_CONFIG
  }
}

// 使用示例
/*
// 在uniapp中使用
import { runAllTests, quickTest } from './debugToolCall测试脚本示例.js'

// 执行完整测试套件
await runAllTests()

// 快速测试单个工具
await quickTest('getTasks', { limit: 5 })

// 测试创建任务
await quickTest('createTask', {
  title: '新任务',
  content: '任务描述',
  priority: 2
})
*/

/**
 * Todo 模块主入口文件
 * 整合认证管理、任务管理、清单管理等功能模块
 * 提供统一的 TodoTool 类接口
 */

const AuthManager = require('./auth')
const TaskManager = require('./tasks')
const ProjectManager = require('./projects')
const { ERROR_CODES } = require('./config')
const TagManager = require('./tags')
const { createErrorResponse } = require('./utils')

/**
 * Todo 工具类
 * 封装所有 todolist 相关功能的主类
 */
class TodoTool {
  constructor() {
    // 初始化各个管理器
    this.authManager = new AuthManager()
    this.taskManager = new TaskManager(this.authManager)
    this.projectManager = new ProjectManager(this.authManager)
    this.tagManager = new TagManager(this.authManager)

    // 基础配置
    this.debug = true

    // 调试用的默认测试 token
    this.debugToken =
      '73AE2E6CC13DD9673F421A1F3E02AED0E1BFB595FD663AFA63ED00682C85E0350ECBA76C0D9169C1842C895EC3C7FD43FA4BB3D094DAFA93E6FC18AA49B4F5302701265667560665A0D14835FCC55972EB9036F52182EC2D6CFEC251B6B3AD83385AA04082B6E13207380EE6E17F65D7D02746F0B1CB9D088DFB1EDE0D3D45D112B6963F72E74B8898CEFB2AD56ED90B75338A509771CA53C093C355F178EA86151002FFD8A51141ED48EB889B07BD4E'

    // 性能优化：连接复用
    this.connectionPool = new Map()
    this.maxConnections = 10
    this.connectionTimeout = 30 * 1000 // 30 秒连接超时

    // 性能统计
    this.stats = {
      requestCount: 0,
      avgResponseTime: 0,
      lastResetTime: Date.now(),
    }

    // 缓存机制
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5 分钟缓存过期时间

    console.log('[TodoTool] 内置 Todo 工具初始化完成')

    // 如果开启调试模式，自动初始化 token
    if (this.debug) {
      this._initDebugMode()
    }
  }

  /**
   * 调试模式初始化
   * 当 debug 开关为 true 时，自动调用 initWithToken 方法进行初始化
   * @private
   */
  async _initDebugMode() {
    try {
      console.log('[TodoTool] [_initDebugMode] 调试模式已开启，正在使用测试 token 初始化...')
      const result = await this.authManager.initWithToken(this.debugToken)
      if (result.errCode) {
        console.error('[TodoTool] [_initDebugMode] 调试模式初始化失败：', result)
      } else {
        console.log('[TodoTool] [_initDebugMode] 调试模式初始化成功')
      }
    } catch (error) {
      console.error('[TodoTool] [_initDebugMode] 调试模式初始化异常：', error)
    }
  }

  /**
   * 统一的工具执行入口
   * @param {string} method - 方法名
   * @param {object} parameters - 参数对象
   * @returns {object} 执行结果
   */
  async execute(method, parameters = {}) {
    const executeStartTime = Date.now()

    console.log(`[TodoTool] [execute] 方法开始：${method}`)
    console.log('[TodoTool] [execute] 输入参数', { method, parameters })

    try {
      // 确保认证状态（精简日志）
      await this.authManager.ensureAuthenticated()

      // 路由到具体方法

      let result
      const methodStartTime = Date.now()

      switch (method) {
        // 认证相关方法
        case 'login':
          result = await this.authManager.login(parameters)
          break
        case 'initWithToken':
          result = await this.authManager.initWithToken(parameters.token)
          break
        case 'getBatchData':
          result = await this.authManager.getBatchData()
          break

        // 任务管理方法
        case 'getTasks':
          result = await this.taskManager.getTasks(parameters)
          break
        case 'createTask':
          result = await this.taskManager.createTask(parameters)
          break
        case 'updateTask': {
          const { taskId, ...updateData } = parameters || {}
          result = await this.taskManager.updateTask(taskId, updateData)
          break
        }
        case 'deleteTask':
          result = await this.taskManager.deleteTask(parameters.taskId)
          break
        case 'getTask':
          result = await this.taskManager.getTask(parameters.taskId)
          break

        // 清单管理方法
        case 'getProjects':
          result = await this.projectManager.getProjects(parameters)
          break
        case 'createProject':
          result = await this.projectManager.createProject(parameters)
          break
        case 'updateProject':
          result = await this.projectManager.updateProject(parameters.projectId, parameters.updateData)
          break
        case 'deleteProject':
          result = await this.projectManager.deleteProject(parameters.projectId)
          break
        case 'getProject':
          result = await this.projectManager.getProject(parameters.projectId)
          break

        // 标签管理方法
        case 'getTags':
          result = await this.tagManager.getTags()
          break
        case 'createTag':
          result = await this.tagManager.createTag(parameters)
          break
        case 'updateTag':
          result = await this.tagManager.updateTag(parameters)
          break
        case 'deleteTag':
          result = await this.tagManager.deleteTag(parameters)
          break
        case 'renameTag':
          result = await this.tagManager.renameTag(parameters)
          break
        case 'mergeTags':
          result = await this.tagManager.mergeTags(parameters)
          break

        // 工具：时间信息
        case 'getCurrentTimeInfo': {
          const { buildTimeContext } = require('../time-context')
          const { now, timeInfo, dates, raw } = buildTimeContext()
          const format = parameters.format || 'detailed'

          let timeData = {}
          switch (format) {
            case 'iso':
              timeData = {
                current_datetime: now.toISOString(),
                today: now.toISOString().split('T')[0],
                tomorrow: raw.tomorrow.toISOString().split('T')[0],
                yesterday: raw.yesterday.toISOString().split('T')[0],
              }
              break
            case 'local':
              timeData = {
                current_date: now.toLocaleDateString('zh-CN'),
                current_time: now.toLocaleTimeString('zh-CN'),
                today: now.toLocaleDateString('zh-CN'),
                tomorrow: dates.tomorrow,
                yesterday: dates.yesterday,
              }
              break
            case 'detailed':
            default:
              timeData = {
                current_datetime: timeInfo.current_datetime,
                current_date: timeInfo.current_date,
                current_time: timeInfo.current_time,
                current_year: timeInfo.current_year,
                current_month: timeInfo.current_month,
                current_day: timeInfo.current_day,
                current_hour: timeInfo.current_hour,
                current_minute: timeInfo.current_minute,
                current_weekday: timeInfo.current_weekday,
                timezone: timeInfo.timezone,
                unix_timestamp: timeInfo.unix_timestamp,
                relative_times: {
                  今天: dates.today || timeInfo.current_date,
                  明天: dates.tomorrow,
                  后天: dates.dayAfterTomorrow,
                  昨天: dates.yesterday,
                  前天: dates.dayBeforeYesterday,
                  今晚: `${timeInfo.current_date} 23:59:59`,
                  明天早上: `${dates.tomorrow} 09:00:00`,
                  明天晚上: `${dates.tomorrow} 23:59:59`,
                  本周末: dates.thisSaturday,
                  这个周末: dates.thisSunday,
                  下周一: dates.nextMonday,
                  下周: dates.nextWeek,
                  本月底: `${dates.lastDayOfMonth} 23:59:59`,
                  下个月初: `${dates.firstDayOfNextMonth} 09:00:00`,
                  下月底: `${dates.lastDayOfNextMonth} 23:59:59`,
                },
                format_examples: {
                  'YYYY-MM-DD': now.toISOString().split('T')[0],
                  'YYYY-MM-DD HH:MM:SS': `${now.toISOString().split('T')[0]} ${now.toTimeString().split(' ')[0]}`,
                  'MM-DD': `${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`,
                  中文日期: timeInfo.current_date,
                },
              }
              break
          }

          result = {
            success: true,
            data: timeData,
            message: `成功获取当前时间信息（格式：${format}）`,
            current_time: now.toLocaleString('zh-CN'),
          }
          break
        }

        default:
          const error = new Error(`未知的方法：${method}`)
          console.error(`[TodoTool] [execute] 未知方法`, {
            method,
            availableMethods: [
              'login',
              'initWithToken',
              'getBatchData',
              'getTasks',
              'createTask',
              'updateTask',
              'deleteTask',
              'getTask',
              'getProjects',
              'createProject',
              'updateProject',
              'deleteProject',
              'getProject',
              'getTags',
              'createTag',
              'updateTag',
              'deleteTag',
              'renameTag',
              'mergeTags',
              'getCurrentTimeInfo',
            ],
            error: error.message,
          })
          throw error
      }

      const executeEndTime = Date.now()
      // 针对 getTasks 补充结果摘要日志
      if (method === 'getTasks') {
        const size = Array.isArray(result?.data) ? result.data.length : -1
        console.log('[TodoTool] [execute] getTasks 结果摘要', {
          listSize: size,
          preview: Array.isArray(result?.data) ? result.data.slice(0, 2) : undefined,
        })
      }

      console.log(`[TodoTool] [execute] === 方法执行成功完成 ===`, {
        method,
        totalDuration: executeEndTime - executeStartTime,
        methodDuration: executeEndTime - methodStartTime,
        endTime: new Date(executeEndTime).toISOString(),
        resultStatus:
          result?.errCode !== undefined
            ? result.errCode === null || result.errCode === 0
              ? 'success'
              : 'error'
            : 'unknown',
        resultErrCode: result?.errCode,
        resultErrMsg: result?.errMsg,
        hasResultData: !!result?.data,
        result,
        success: true,
      })

      return result
    } catch (error) {
      const executeEndTime = Date.now()

      console.error(`[TodoTool] [execute] === 方法执行异常 ===`, error, {
        method,
        parameters: JSON.stringify(parameters, null, 2),
        errorName: error.name,
        errorMessage: error.message,
        errorStack: error.stack,
        totalDuration: executeEndTime - executeStartTime,
        endTime: new Date(executeEndTime).toISOString(),
        success: false,
      })

      // 特别关注任务创建相关的错误
      if (method === 'createTask') {
        console.error(`[TodoTool] [execute] !!! 创建任务方法执行失败 !!!`, error, {
          method,
          inputParameters: JSON.stringify(parameters, null, 2),
          errorDetails: {
            name: error.name,
            message: error.message,
            stack: error.stack,
          },
          totalDuration: executeEndTime - executeStartTime,
          timestamp: new Date().toISOString(),
        })
      }

      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message, error)
    }
  }

  /**
   * 性能统计：更新统计信息
   * @param {number} responseTime - 响应时间
   */
  _updateStats(responseTime) {
    this.stats.requestCount++

    // 计算平均响应时间
    this.stats.avgResponseTime =
      (this.stats.avgResponseTime * (this.stats.requestCount - 1) + responseTime) / this.stats.requestCount
  }

  /**
   * 获取性能统计信息
   * @returns {object} 性能统计数据
   */
  getPerformanceStats() {
    const now = Date.now()
    const uptime = now - this.stats.lastResetTime

    return {
      ...this.stats,
      uptime: uptime,
      isAuthenticated: this.authManager.isAuthenticated,
    }
  }

  /**
   * 确保认证状态
   * 实现认证状态复用，避免重复认证
   */
  async ensureAuthenticated() {
    return await this.authManager.ensureAuthenticated()
  }
}

module.exports = TodoTool

<template>
  <div class="chat-input-container">
    <div class="toolbar-container">
      <div class="toolbar-scroll">
        <slot name="toolbar"></slot>
      </div>
    </div>
    <div class="input-area">
      <div class="icon-btn mic-btn" @click="toggleRecording">
        <i :class="['fas', isRecording ? 'fa-stop' : 'fa-microphone']"></i>
      </div>

      <div v-if="uploadProgress.show" class="upload-state">
        <div class="segmented-progress-bar">
          <div
            v-for="i in 24"
            :key="i"
            class="progress-segment"
            :class="{ active: i <= (uploadProgress.percent * 24) / 100 }"
          ></div>
        </div>
        <div class="progress-text">{{ uploadProgress.percent }}%</div>
      </div>
      <div v-else-if="isRecording" class="recording-state" @click="pauseOrResumeRecording">
        <div class="wave-container">
          <div class="wave-bar" v-for="n in 7" :key="n" :style="{ height: getWaveHeight(n) + 'px' }"></div>
        </div>
        <span class="recording-timer">{{ formatDuration(duration) }}</span>
        <span class="recording-hint">{{ isPaused ? '已暂停' : '录音中...' }}</span>
      </div>

      <div v-else-if="mode === 'audio-only'" class="audio-only-placeholder">
        <span>点击左侧按钮开始录音</span>
      </div>

      <div v-else-if="mode === 'full'" class="text-input-wrapper">
        <u-input
          v-model="inputValue"
          :placeholder="placeholder"
          @confirm="sendMessage"
          type="textarea"
          :auto-height="true"
          maxlength="5000"
          :height="20"
          :cursor-spacing="15"
          :border="false"
          :show-confirm-bar="false"
          :custom-style="{ backgroundColor: 'transparent', color: 'var(--color-gray-800)', fontSize: '15px' }"
          @focus="handleInputFocus"
          @blur="handleInputBlur"
        />
      </div>

      <!-- 处理中显示“停止/打断”按钮 -->
      <div v-if="processing" class="icon-btn stop-btn" @click="handleStop">
        <i class="fas fa-stop"></i>
      </div>

      <div
        v-if="!processing && (mode === 'full' || (mode === 'audio-only' && isRecording))"
        class="icon-btn send-btn"
        :class="{ active: inputValue.trim() || isRecording }"
        @click="handleSend"
      >
        <i class="fas fa-paper-plane"></i>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, nextTick, onUnmounted } from 'vue'
import useRecord from '@/hooks/useRecord'
import dayjs from 'dayjs'
import request from '@/utils/request'
import { getPlatform } from '@/utils/tools'

console.log('[DEBUG] l-message-input 组件脚本开始加载：', new Date().toISOString())

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '输入消息...',
  },
  // 新增 UI 模式
  mode: {
    type: String,
    default: 'full', // 'full' or 'audio-only'
  },
  // 移除 enableTranscription 属性
  maxDuration: {
    type: Number,
    default: 60000, // 默认最长录音时间 1 分钟
  },
  audioFormat: {
    type: String,
    default: 'mp3',
  },
  cloudPath: {
    type: String,
    default: '', // 为空时使用默认路径规则
  },
  // AI 处理中（用于显示“停止”图标并触发打断）
  processing: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'send', 'send-audio', 'upload-progress', 'error', 'stop'])

console.log('[DEBUG] l-message-input props:', JSON.stringify(props))

const inputValue = ref(props.modelValue)
console.log('[DEBUG] l-message-input inputValue 初始化：', inputValue.value)

const uploadProgress = ref({ show: false, percent: 0 })
console.log('[DEBUG] l-message-input uploadProgress 初始化：', JSON.stringify(uploadProgress.value))

// 工具按钮通过插槽由父组件自定义提供

// 输入框聚焦状态
const isInputFocused = ref(false)

const handleKeyDown = (event) => {
  if (isInputFocused.value && event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSend()
  }
}

// 触发停止（打断）事件，由父组件处理取消逻辑
const handleStop = () => {
  uni.vibrateShort()
  emit('stop')
}

const handleInputFocus = () => {
  isInputFocused.value = true
  if (typeof document !== 'undefined') {
    document.addEventListener('keydown', handleKeyDown)
  }
}

const handleInputBlur = () => {
  isInputFocused.value = false
  if (typeof document !== 'undefined') {
    document.removeEventListener('keydown', handleKeyDown)
  }
}

// 使用录音 hook
console.log('[DEBUG] l-message-input 开始初始化 useRecord hook')
const {
  isRecording,
  isPaused,
  volume,
  duration,
  recordBlob,
  startRecording,
  pauseRecording,
  resumeRecording,
  stopRecording,
  cancelRecording,
} = useRecord({
  maxDuration: props.maxDuration,
  mimeType: 'audio/webm',
  appOptions: {
    format: props.audioFormat,
  },
})
console.log('[DEBUG] l-message-input useRecord hook 初始化完成')

// 记录当前音量，用于动态生成波形
const currentVolume = ref(0)
watch(volume, (newVolume) => {
  currentVolume.value = newVolume
})

// 双向绑定
watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue
  }
)

watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 切换录音状态
const toggleRecording = async () => {
  uni.vibrateShort()
  if (isRecording.value) {
    // 停止录音
    try {
      const filePath = await stopRecording()
      console.log('录音完成', filePath)

      // 确保录音状态被正确重置
      isRecording.value = false
    } catch (error) {
      console.error('停止录音失败：', error)
      // 即使停止失败也重置录音状态
      isRecording.value = false
      cancelRecording()
    }
  } else {
    // 开始录音
    try {
      await startRecording()
      console.log('开始录音...')
    } catch (error) {
      console.error('开始录音失败：', error)
      // 可能是用户拒绝了麦克风权限
      uni.showModal({
        title: '录音失败',
        content: '无法启动录音，请检查麦克风权限',
        showCancel: false,
      })
    }
  }
}

// 暂停或继续录音
const pauseOrResumeRecording = () => {
  if (isPaused.value) {
    resumeRecording()
    console.log('继续录音')
  } else {
    pauseRecording()
    console.log('暂停录音')
  }
}

// 根据索引和当前音量生成波形高度
const getWaveHeight = (index) => {
  // 基础高度
  const baseHeight = 12

  // 如果暂停了，所有波形固定高度
  if (isPaused.value) {
    return baseHeight
  }

  // 根据当前音量计算高度倍数
  const volumeFactor = currentVolume.value / 100

  // 波形高度上限
  const maxHeight = 24

  // 使用索引创建错位效果
  const indexFactor = Math.sin(Date.now() / 500 + index * 0.7)

  // 计算最终高度
  return baseHeight + Math.max(0, indexFactor * volumeFactor * maxHeight)
}

// 格式化录音时长
const formatDuration = (ms) => {
  const totalSeconds = Math.floor(ms / 1000)
  const minutes = Math.floor(totalSeconds / 60)
    .toString()
    .padStart(2, '0')
  const seconds = (totalSeconds % 60).toString().padStart(2, '0')
  return `${minutes}:${seconds}`
}

// 上传音频文件
const uploadAudio = async (blob, filePath) => {
  try {
    uploadProgress.value = { show: true, percent: 0 }

    // 生成随机 UUID，确保文件名唯一
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0,
        v = c == 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })

    const cloudPath = `${props.cloudPath}${dayjs().format('YYYY-MM')}/${Date.now()}_${uuid}.${props.audioFormat}`
    console.log('上传路径：', filePath, cloudPath)
    const uploadResult = await uniCloud.uploadFile({
      filePath,
      cloudPath,
      cloudPathAsRealPath: true,
      onUploadProgress: (e) => {
        const percent = Math.round((e.loaded / e.total) * 100)
        uploadProgress.value.percent = percent
        emit('upload-progress', { percent })
      },
    })
    console.log('上传结果 zzz:', uploadResult)
    uploadProgress.value.percent = 100
    setTimeout(() => {
      uploadProgress.value.show = false
    }, 500)
    console.log('上传结果 aaa:', uploadResult)
    const { fileList } = await uniCloud.getTempFileURL({ fileList: [uploadResult.fileID] })
    const audioUrl = fileList[0].tempFileURL
    console.log('上传结果 bbb:', audioUrl)
    return { fileID: uploadResult.fileID, tempFileURL: audioUrl }
  } catch (error) {
    uploadProgress.value.show = false
    emit('error', { type: 'upload', error })
    throw error
  }
}

// 处理发送
const handleSend = async () => {
  if (!isRecording.value && !inputValue.value.trim()) {
    return
  }
  uni.vibrateShort()

  if (isRecording.value) {
    // 发送录音
    try {
      console.log('准备停止录音...')
      // 先停止录音，修复点击发送后还在录音的问题
      const filePath = await stopRecording().catch((e) => {
        console.error('停止录音具体错误：', e)
        throw { step: '停止录音', error: e, message: '停止录音失败：' + (e.message || String(e)) }
      })
      console.log('录音已停止，获取到文件路径：', filePath)

      // 确保录音状态被正确重置
      isRecording.value = false

      // 上传文件
      console.log('准备上传文件...')
      let uploadResult
      try {
        uploadResult = await uploadAudio(recordBlob.value, filePath)
        console.log('文件上传成功：', uploadResult)
      } catch (e) {
        console.error('上传文件错误：', e)
        throw { step: '上传文件', error: e, message: '上传文件失败：' + (e.message || String(e)) }
      }

      // 发送录音事件
      console.log('准备发送录音事件...')
      emit('send-audio', {
        blob: recordBlob.value,
        duration: duration.value,
        fileID: uploadResult.fileID,
        tempFileURL: uploadResult.tempFileURL,
      })

      // 重置录音状态
      console.log('重置录音状态...')
      cancelRecording()
    } catch (error) {
      console.error('发送录音详细错误：', error)
      // 确保录音状态被重置
      cancelRecording()
      isRecording.value = false

      // 使用更详细的错误信息
      emit('error', {
        type: 'send',
        error: error,
        step: error.step || '未知步骤',
        message: error.message || '发送录音失败',
      })
    }
  } else if (inputValue.value.trim()) {
    // 发送文本
    sendMessage()
  }
}

// 发送消息
const sendMessage = () => {
  if (inputValue.value.trim()) {
    emit('send')
  }
}

// 在组件卸载时重置录音状态
onUnmounted(() => {
  // 清理键盘事件监听
  if (typeof document !== 'undefined' && isInputFocused.value) {
    document.removeEventListener('keydown', handleKeyDown)
  }

  if (isRecording.value) {
    cancelRecording()
    isRecording.value = false
  }
})
</script>

<style lang="scss" scoped>
.chat-input-container {
  padding: 8px 12px;
  background-color: var(--color-white);
  border-top: 1px solid var(--color-gray-200);
  position: relative;
}

/* 工具按钮栏 */
.toolbar-container {
  margin-bottom: 6px;
}

.toolbar-scroll {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  padding: 2px 2px 6px;
}

/* 滚动条样式（尽量轻量）*/
.toolbar-scroll::-webkit-scrollbar {
  height: 4px;
}
.toolbar-scroll::-webkit-scrollbar-track {
  background: transparent;
}
.toolbar-scroll::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: 4px;
}

.toolbar-scroll {
  :deep(.toolbar-btn) {
    flex: 0 0 auto;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    background-color: var(--color-gray-100);
    border-radius: 16px;
    color: var(--color-gray-700);
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid var(--color-gray-200);
  }

  :deep(.toolbar-btn i) {
    font-size: 14px;
    color: var(--color-gray-700);
  }

  :deep(.toolbar-btn:hover) {
    background-color: var(--color-primary);
    color: var(--color-white);
    border-color: var(--color-primary);
  }

  :deep(.toolbar-btn:hover i) {
    color: var(--color-white);
  }

  :deep(.toolbar-btn .label) {
    line-height: 1;
    white-space: nowrap;
  }
}

.input-area {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: var(--color-gray-100);
  border-radius: 24px;
  padding: 4px;
}

.icon-btn {
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--color-gray-600);
  font-size: 18px;
  transition: all 0.2s;

  &:hover {
    color: var(--color-primary);
  }
}

.mic-btn {
  background-color: var(--color-primary);
  border-radius: 50%;
  opacity: 0.9;
  i {
    color: var(--color-white);
  }
}

.send-btn {
  background-color: var(--color-primary);
  border-radius: 50%;
  opacity: 0.5;

  i {
    color: var(--color-white);
  }

  &.active {
    opacity: 1;
  }
}

.stop-btn {
  background-color: var(--color-danger, #ef4444);
  border-radius: 50%;
  margin-left: 4px;

  i {
    color: var(--color-white);
  }

  &:hover {
    filter: brightness(1.05);
  }
}

.text-input-wrapper {
  flex: 1;
  margin: 0 4px;
  display: flex;
  align-items: center;
}

.recording-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  color: var(--color-primary);
  cursor: pointer;
}

.wave-container {
  display: flex;
  align-items: center;
  height: 24px;
}

.wave-bar {
  width: 3px;
  background-color: var(--color-primary);
  margin: 0 2px;
  border-radius: 1px;
  transition: height 0.1s ease-in-out;
}

.recording-timer {
  font-size: 14px;
  font-weight: 500;
}

.recording-hint {
  font-size: 12px;
  color: var(--color-gray-600);
  margin-left: 8px;
}

.upload-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
}

.segmented-progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
}

.progress-segment {
  width: 4px;
  height: 18px;
  margin: 0 1.5px;
  border-radius: 2px;
  background-color: var(--color-gray-300);
  transition: background-color 0.3s ease;

  &.active {
    background-color: var(--color-primary);
  }
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
  margin-left: 10px;
  min-width: 40px;
  text-align: left;
}

.audio-only-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  color: var(--color-gray-600);
  font-size: 14px;
}
</style>

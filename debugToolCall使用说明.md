# debugToolCall 调试函数使用说明

## 功能概述

`debugToolCall` 是一个专门用于调试工具调用的云函数，它绕过AI模型，直接测试指定的工具函数执行情况。这个函数帮助开发者在不触发完整AI对话流程的情况下，快速验证各个工具函数的正确性。

## 函数签名

```javascript
async debugToolCall({ toolName, parameters })
```

## 参数说明

### 输入参数
- `toolName` (string, 必填): 要调试的工具名称
- `parameters` (object, 必填): 工具执行所需的参数对象

### 返回值
返回标准的响应对象，包含：
- `errCode`: 错误码 (0表示成功)
- `errMsg`: 错误或成功消息
- `data`: 详细的执行结果数据
  - `toolName`: 工具名称
  - `parameters`: 输入参数
  - `result`: 工具执行结果 (成功时)
  - `error`: 错误信息 (失败时)
  - `executionTime`: 执行耗时(毫秒)
  - `debugMode`: 调试模式标识 (始终为 true)
  - `sessionId`: 调试会话ID
  - `timestamp`: 执行时间戳

## 支持的工具列表

基于 TodoTool 支持的所有工具方法：

### 认证相关
- `login` - 用户登录
- `initWithToken` - 使用Token初始化

### 任务管理
- `getTasks` - 获取任务列表
- `createTask` - 创建任务
- `updateTask` - 更新任务
- `deleteTask` - 删除任务
- `getTask` - 获取单个任务

### 项目管理
- `getProjects` - 获取项目列表
- `createProject` - 创建项目
- `updateProject` - 更新项目
- `deleteProject` - 删除项目
- `getProject` - 获取单个项目

### 标签管理
- `getTags` - 获取标签列表
- `createTag` - 创建标签
- `updateTag` - 更新标签
- `deleteTag` - 删除标签
- `renameTag` - 重命名标签
- `mergeTags` - 合并标签

### 其他
- `getBatchData` - 获取批量数据
- `getCurrentTimeInfo` - 获取当前时间信息

## 使用示例

### 1. 测试获取任务列表

```javascript
// 调用示例
const result = await uniCloud.callFunction({
  name: 'ai',
  data: {
    action: 'debugToolCall',
    toolName: 'getTasks',
    parameters: {
      mode: 'all',
      limit: 10,
      completed: false
    }
  }
})

console.log('调试结果:', result)
```

**预期成功响应:**
```javascript
{
  errCode: 0,
  errMsg: 'success',
  data: {
    toolName: 'getTasks',
    parameters: { mode: 'all', limit: 10, completed: false },
    result: {
      errCode: 0,
      message: '获取任务成功',
      data: [/* 任务列表 */]
    },
    executionTime: 1250,
    debugMode: true,
    sessionId: 'debug_1703123456789_abc12',
    timestamp: '2023-12-21T03:30:56.789Z'
  }
}
```

### 2. 测试创建任务

```javascript
const result = await uniCloud.callFunction({
  name: 'ai',
  data: {
    action: 'debugToolCall',
    toolName: 'createTask',
    parameters: {
      title: '测试任务',
      content: '这是一个测试任务',
      priority: 1,
      projectName: '工作',
      dueDate: '2023-12-25'
    }
  }
})
```

### 3. 测试获取项目列表

```javascript
const result = await uniCloud.callFunction({
  name: 'ai',
  data: {
    action: 'debugToolCall',
    toolName: 'getProjects',
    parameters: {}
  }
})
```

### 4. 测试参数验证错误

```javascript
// 缺少必填参数的情况
const result = await uniCloud.callFunction({
  name: 'ai',
  data: {
    action: 'debugToolCall',
    toolName: 'createTask',
    parameters: {
      // 缺少 title 参数
      content: '没有标题的任务'
    }
  }
})
```

**预期错误响应:**
```javascript
{
  errCode: 'TOOL_EXECUTION_ERROR',
  errMsg: '参数校验失败：title 是必填参数',
  data: {
    toolName: 'createTask',
    parameters: { content: '没有标题的任务' },
    error: '参数校验失败：title 是必填参数',
    executionTime: 45,
    debugMode: true,
    sessionId: 'debug_1703123456789_def34',
    timestamp: '2023-12-21T03:30:56.834Z'
  }
}
```

## 日志输出

调试函数使用与正常工具调用相同的日志格式，便于统一查看和分析：

### 成功调用日志
```
[工具调用] getTasks - 入参: {
  sessionId: 'debug_1703123456789_abc12',
  toolName: 'getTasks',
  parameters: { mode: 'all', limit: 10 },
  toolCallId: 'debug_debug_1703123456789_abc12',
  debugMode: true
}

[工具调用] getTasks - 出参: {
  sessionId: 'debug_1703123456789_abc12',
  toolName: 'getTasks',
  success: true,
  dataCount: 5,
  message: '获取任务成功',
  executionTime: 1250,
  errCode: 0,
  debugMode: true
}
```

### 错误调用日志
```
[工具调用] createTask - 出参(错误): {
  sessionId: 'debug_1703123456789_def34',
  toolName: 'createTask',
  success: false,
  error: '参数校验失败：title 是必填参数',
  executionTime: 45,
  debugMode: true,
  toolCallId: 'debug_debug_1703123456789_def34'
}
```

## 错误码说明

- `0`: 成功
- `PARAM_IS_NULL`: 必填参数为空
- `PARAM_INVALID`: 参数格式无效
- `TOOL_EXECUTION_ERROR`: 工具执行失败

## 使用场景

1. **功能验证**: 验证新开发的工具函数是否正常工作
2. **参数测试**: 测试不同参数组合的执行结果
3. **错误调试**: 快速定位工具函数中的问题
4. **性能测试**: 测量工具函数的执行时间
5. **集成测试**: 在集成AI对话前先验证工具函数

## 注意事项

1. **调试模式标识**: 所有返回结果都包含 `debugMode: true` 标识
2. **会话ID**: 每次调用都会生成唯一的调试会话ID
3. **日志格式**: 与正常工具调用使用相同的日志格式，便于统一分析
4. **错误处理**: 完整的错误处理机制，包含详细的错误信息
5. **执行时间**: 精确记录工具函数的执行耗时

## 最佳实践

1. **参数准备**: 在调试前准备好完整的测试参数
2. **日志查看**: 通过云函数日志查看详细的执行过程
3. **错误分析**: 利用返回的错误信息快速定位问题
4. **性能监控**: 关注执行时间，优化性能瓶颈
5. **批量测试**: 可以编写脚本批量测试多个工具函数

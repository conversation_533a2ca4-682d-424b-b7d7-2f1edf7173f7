# AI 工具直连 todoTool 重构需求

## 背景
当前 `uniCloud-aliyun/cloudfunctions/ai/index.obj.js` 中，AI 工具调用通过一层适配函数（如 `executeGetTasks`、`executeCreateTask` 等）再间接调用 `TodoTool`。为简化结构、减少一层封装，并按照最新需求“工具仅供 AI 使用、允许调整数据结构”，计划移除该适配层，改为在 `switch` 中直接调用 `TodoTool`。

价值与目标：
- 降低耦合与心智负担，减少重复日志与数据组装代码
- 便于快速演进工具入参/出参，无需同步维护两层签名
- 保留 AI 会话必要能力（SSE 推送、工具结果回灌）
- 以“最佳实践”为准：允许同步改动 `todoTool` 内部入参、默认值、校验与返回结构

## 需求

### 功能需求
- 删除以下适配层函数定义及其引用：
  - `executeGetTasks`
  - `executeCreateTask`
  - `executeGetProjects`
  - `executeUpdateTask`
- 在 `executeToolCall` 的 `switch` 分支内直接实例化并调用 `TodoTool` 对应方法：
  - `getTasks` / `createTask` / `getProjects` / `updateTask`
- 对 `uniCloud-aliyun/cloudfunctions/ai/modules/todo`（含 `index.js`、`tasks.js`、`projects.js` 等）进行必要改造：
  - 入参层：接收 AI 语义友好的参数（如 `completed`、`limit`、`projectName`、`priority` 等），在工具内部完成默认值填充、类型校验与语义映射（如 `completed -> status`）。
  - 业务层：统一时间字段为 ISO 8601 字符串或明确的 `YYYY-MM-DD`/`YYYY-MM-DD HH:mm:ss`，必要时做时区一致性处理（Asia/Shanghai）。
  - 出参层：输出统一结构，推荐 `{ success: boolean, message?: string, data?: any }`；错误通过抛出具名错误（含 `code`/`message`）。
  - 支持分页/裁剪：在工具层支持 `limit`/`offset` 或最少 `limit`。
- `getCurrentTimeInfo` 属于通用时间能力，不依赖 `TodoTool`，保留为独立实现即可（也可内联到 `switch`，不强制）。
- 保留并继续使用现有的 SSE 事件推送语义：
  - `TOOL_EXECUTION_START` / `TOOL_EXECUTION_COMPLETE` / `TOOL_EXECUTION_ERROR`

### 非功能需求
- 代码可读性：`switch` 分支逻辑精简清晰，避免深层嵌套
- 性能：不引入额外的同步阻塞；日志与错误处理开销与原方案相当或更低
- 监控与可观测性：保留关键日志（入参/出参、错误信息）与 SSE 事件

## 技术方案

### 文件与范围
- 修改：`uniCloud-aliyun/cloudfunctions/ai/index.obj.js`
- 修改：`uniCloud-aliyun/cloudfunctions/ai/modules/todo` 目录下相关文件（`index.js`、`tasks.js`、`projects.js`、`utils.js` 等）

### 实现步骤
1. 工具层改造（modules/todo）
   - 为 `getTasks/createTask/getProjects/updateTask` 增加参数校验与默认值设置；在工具内部完成语义映射（`completed -> status`）与时间格式规范化。
   - 统一返回结构 `{ success, message, data }`，异常通过具名错误抛出（如 `new DomainError(code, message)`）。
   - `getTasks` 支持 `limit`（与可选 `offset`），返回前完成裁剪。
2. AI 层精简（index.obj.js）
   - 在 `executeToolCall` 中直接调用 `todoTool`；删除 `execute*` 适配层函数。
   - 接收工具层统一结构，原样回灌或稍作包装给模型。
   - `getCurrentTimeInfo` 保持独立实现。
3. 保持原有 SSE 推送位点不变：
   - 调用前：`TOOL_EXECUTION_START`
   - 成功后：`TOOL_EXECUTION_COMPLETE`
   - 异常时：`TOOL_EXECUTION_ERROR`
4. 验证与联调：运行并验证 AI 工具调用流程（详见“验收标准”）

### 工具接口规范（重定义）
- getTasks(params)
  - 入参：`{ keyword?: string, completed?: boolean, projectName?: string, priority?: number|string, limit?: number, offset?: number }`
  - 行为：内部将 `completed` 映射为符合业务的 `status`；应用 `limit/offset`；返回任务数组
  - 出参：`{ success: true, data: Task[], message?: string }`
- createTask(params)
  - 入参：`{ title: string, content?: string, priority?: number|string, projectName?: string, tagNames?: string[], startDate?: string, dueDate?: string, isAllDay?: boolean, reminder?: any }`
  - 行为：默认 `kind='TEXT'`；规范化时间字段格式；校验必填字段
  - 出参：`{ success: true, data: CreatedTask, message?: string }`
- getProjects(params)
  - 入参：`{ keyword?: string, includeClosed?: boolean }`
  - 出参：`{ success: true, data: Project[], message?: string }`
- updateTask(taskId, data)
  - 入参：`taskId: string`, `data` 可含 `title/content/priority/projectName/completed/dueDate/startDate`
  - 行为：内部将 `completed` 映射为 `status`（2/0）；规范化时间格式
  - 出参：`{ success: true, data: UpdatedTask, message?: string }`

### 错误处理规范
- 工具层抛出具名错误（如 `DomainError`），包含 `code` 与 `message`；AI 层捕获并通过 `TOOL_EXECUTION_ERROR` 推送。
- 常见错误码建议：`INVALID_PARAMS`、`NOT_FOUND`、`UPSTREAM_ERROR`、`RATE_LIMITED`、`UNKNOWN_ERROR`。

### 数据格式规范
- 时间/日期：
  - 日期时间字段：ISO 8601 字符串，如 `2025-05-24T02:26:08.286Z`
  - 纯日期字段：`YYYY-MM-DD`，如 `2025-05-24`
  - 若返回带时刻的中文本地化展示，保持在 AI 层完成，不污染工具层数据结构

### 新的调用示例（伪代码）
```js
// executeToolCall 内
const todoTool = new TodoTool()
let result
switch (toolName) {
  case 'getTasks': {
    const options = {
      mode: 'all',
      keyword: parameters.keyword ?? null,
      completed: parameters.completed,
      projectName: parameters.projectName ?? null,
      priority: parameters.priority ?? null,
    }
    const r = await todoTool.getTasks(options)
    if (r.errCode) throw new Error(r.errMsg)
    const tasks = Array.isArray(r.data) ? (parameters.limit > 0 ? r.data.slice(0, parameters.limit) : r.data) : []
    result = { success: true, message: `成功获取 ${tasks.length} 个任务`, data: tasks }
    break
  }
  case 'createTask': {
    const r = await todoTool.createTask({
      title: parameters.title,
      content: parameters.content ?? null,
      priority: parameters.priority ?? null,
      projectName: parameters.projectName ?? null,
      tagNames: parameters.tagNames ?? null,
      startDate: parameters.startDate,
      dueDate: parameters.dueDate,
      isAllDay: parameters.isAllDay ?? null,
      reminder: parameters.reminder ?? null,
      kind: 'TEXT',
    })
    if (r.errCode) throw new Error(r.errMsg)
    result = { success: true, message: `任务"${r.data.title}"已创建成功！`, data: r.data }
    break
  }
  case 'getProjects': {
    const r = await todoTool.getProjects({
      keyword: parameters.keyword ?? null,
      includeClosed: parameters.includeClosed ?? false,
    })
    if (r.errCode) throw new Error(r.errMsg)
    result = { success: true, message: `成功获取 ${r.data?.length || 0} 个项目`, data: r.data }
    break
  }
  case 'updateTask': {
    const updateData = { }
    if (parameters.title !== undefined) updateData.title = parameters.title
    if (parameters.content !== undefined) updateData.content = parameters.content
    if (parameters.priority !== undefined) updateData.priority = parameters.priority
    if (parameters.projectName !== undefined) updateData.projectName = parameters.projectName
    if (parameters.completed !== undefined) updateData.status = parameters.completed ? 2 : 0
    if (parameters.dueDate !== undefined) updateData.dueDate = parameters.dueDate
    if (parameters.startDate !== undefined) updateData.startDate = parameters.startDate
    const r = await todoTool.updateTask(parameters.taskId, updateData)
    if (r.errCode) throw new Error(r.errMsg)
    result = { success: true, message: '成功更新任务', data: r.data }
    break
  }
  case 'getCurrentTimeInfo': {
    // 复用原实现或内联时间数据构造
    result = await executeGetCurrentTimeInfo(parameters)
    break
  }
  default:
    throw new Error(`未知的工具：${toolName}`)
}
return result
```

### 架构与流程
```mermaid
flowchart LR
  U[用户输入] --> LLM[LLM 推理]
  LLM -->|tool_calls| ET[executeToolCall]
  ET -->|switch| TT[TodoTool]
  TT --> R[结果]
  ET -->|SSE: START/COMPLETE/ERROR| FE[前端SSE]
  R --> ET
  ET --> LLM2[LLM 续写回复]
  LLM2 --> U2[输出给用户]
```

## 风险评估
### 假设与未知因素
- 假设下游 Prompt/前端对工具返回结构的依赖较弱，允许按本方案最小规范化
- 未知是否存在第三方基于旧 `{ success, message, data }` 结构的直接依赖

### 潜在风险与应对
- 结构变更引发兼容性问题：
  - 风险：前端或 Prompt 使用旧字段
  - 对策：优先保持 `{ success, message, data }` 结构；或同步更新 Prompt 与 UI 解析逻辑
- 语义映射缺失（如 `completed -> status`）导致任务状态异常：
  - 对策：在 `switch` 内继续做最小映射，或在 `TodoTool` 内部增强兼容
- 日志缺失导致问题定位困难：
  - 对策：保留入参/出参与错误日志；必要时增加埋点键值

## 验收标准
- 调用 `getTasks/createTask/getProjects/updateTask/getCurrentTimeInfo` 均能在 `switch` 中直连完成
- SSE 事件（START/COMPLETE/ERROR）在每次调用过程按期望推送
- 工具返回可被 `continueConversationWithToolResults` 正常序列化与回灌，对话能续写完整
- 不再存在 `executeGetTasks/executeCreateTask/executeGetProjects/executeUpdateTask` 定义与引用
- 单次回归验证通过：
  - 新建任务（含截止日期/全天）创建成功并回显
  - 更新任务完成状态成功并回显
  - 查询任务和项目能返回合理数据并可按 `limit` 裁剪（如保留）

## 迁移与回滚
### 迁移步骤
1. 删除适配层函数定义并移除引用
2. 在 `switch` 中内联直连逻辑
3. 保持 SSE 与日志位置不变
4. 本地/测试环境联调验证工具调用全链路

### 回滚策略
- 若发现兼容性问题或线上异常，立即回滚到原适配层方案分支
- 或临时恢复 `{ success, message, data }` 统一结构，平滑过渡

## 相关文件
- `uniCloud-aliyun/cloudfunctions/ai/index.obj.js`
- `uniCloud-aliyun/cloudfunctions/ai/modules/todo/index.js` 及其子模块



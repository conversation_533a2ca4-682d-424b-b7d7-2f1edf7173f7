/**
 * 会话管理模块
 * 处理AI会话的持久化存储和状态管理
 */

/**
 * 会话持久化：运行状态 & 取消标记
 * @param {object} db - 数据库实例
 * @param {string} sessionId - 会话ID
 * @param {object} data - 会话数据
 */
async function upsertSession(db, sessionId, data) {
  const now = new Date().toISOString()
  const payload = { updateTime: now, ...(data || {}) }
  if (data && data.status === 'running') {
    payload.createTime = now
    payload.canceled = false
  }
  try {
    const res = await db.collection('aiSession').doc(sessionId).set(payload)
    return res
  } catch (e) {
    console.error('[aiSession] upsertSession.set error', { sessionId, error: e && e.message })
    throw e
  }
}

/**
 * 标记会话状态
 * @param {object} db - 数据库实例
 * @param {string} sessionId - 会话ID
 * @param {object} updates - 更新数据
 */
async function markSession(db, sessionId, updates) {
  const now = new Date().toISOString()
  try {
    const updatePayload = { ...(updates || {}), updateTime: now }
    const res = await db.collection('aiSession').doc(sessionId).update(updatePayload)
    return res
  } catch (e) {
    console.error('[aiSession] markSession.update error', { sessionId, error: e && e.message })
    throw e
  }
}

/**
 * 检查会话是否被取消
 * @param {object} db - 数据库实例
 * @param {string} sessionId - 会话ID
 * @returns {Promise<boolean>} 是否被取消
 */
async function checkSessionCanceled(db, sessionId) {
  try {
    const { data } = await db.collection('aiSession').doc(sessionId).get()
    const doc = Array.isArray(data) ? data[0] : null
    return doc && (doc.canceled === true || doc.status === 'canceled')
  } catch (err) {
    console.error('[checkSessionCanceled] 查询异常', { sessionId, error: err && err.message })
    return false
  }
}

/**
 * 创建会话取消轮询器
 * @param {object} db - 数据库实例
 * @param {string} sessionId - 会话ID
 * @param {function} onCanceled - 取消回调函数
 * @param {number} pollIntervalMs - 轮询间隔（毫秒）
 * @returns {object} 包含停止轮询方法的对象
 */
function createCancelPoller(db, sessionId, onCanceled, pollIntervalMs = 1000) {
  let canceledFlag = false
  
  const pollTimer = setInterval(async () => {
    try {
      if (canceledFlag) return
      
      const isCanceled = await checkSessionCanceled(db, sessionId)
      if (isCanceled) {
        canceledFlag = true
        clearInterval(pollTimer)
        
        try {
          await markSession(db, sessionId, { 
            status: 'canceled', 
            canceled: true, 
            canceledAt: new Date().toISOString() 
          })
        } catch (_) {}
        
        if (typeof onCanceled === 'function') {
          onCanceled()
        }
      }
    } catch (err) {
      console.error('[cancelPoll] 轮询异常', { 
        sessionId, 
        error: err && err.message, 
        stack: err && err.stack 
      })
    }
  }, pollIntervalMs)

  return {
    stop: () => {
      try {
        clearInterval(pollTimer)
      } catch (_) {}
    },
    isCanceled: () => canceledFlag
  }
}

module.exports = {
  upsertSession,
  markSession,
  checkSessionCanceled,
  createCancelPoller,
}

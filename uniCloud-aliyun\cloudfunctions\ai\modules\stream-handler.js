/**
 * 流式响应处理模块
 * 处理AI模型的流式响应和工具调用
 */

const { OpenAI } = require('openai')
const { FUNCTION_TOOLS, doubaoParams, SSE_MESSAGE_TYPES, createSSEMessage } = require('./config.js')
const { inferOperationTypeFromTool, buildDetails } = require('./tool-details.js')
const TodoTool = require('./todo')

/**
 * 统一工具执行接口
 * @param {object} toolCall - 工具调用对象
 * @param {object} sseChannel - SSE通道
 * @param {string} sessionId - 会话ID
 * @returns {Promise<object>} 工具执行结果
 */
async function executeToolCall(toolCall, sseChannel, sessionId) {
  const { function: func } = toolCall
  const toolName = func.name

  try {
    const parameters = JSON.parse(func.arguments)

    // 📝 记录工具调用入参
    console.log(`[工具调用] ${toolName} - 入参:`, {
      sessionId,
      toolName,
      parameters,
      toolCallId: toolCall.id,
    })

    // 推送工具执行开始消息
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_START, sessionId, {
        toolName: toolName,
        parameters: parameters,
      })
    )

    let result
    const todoTool = new TodoTool()

    // 统一通过 TodoTool.execute 路由工具调用
    result = await todoTool.execute(toolName, parameters)

    // 统一错误语义：工具层如返回 errCode，视为失败并抛错
    if (result && result.errCode !== undefined && result.errCode !== null && result.errCode !== 0) {
      throw new Error(result.errMsg || '工具执行失败')
    }

    // 📝 记录工具调用出参
    console.log(`[工具调用] ${toolName} - 出参:`, {
      sessionId,
      toolName,
      success: true,
      dataCount: Array.isArray(result?.data) ? result.data.length : 0,
      message: result?.message || '',
      executionTime: result?.executionTime || null,
      errCode: result?.errCode || 0,
    })

    // 推送工具执行完成消息（仅发送精简摘要，避免 SSE 过大）
    const dataCount = Array.isArray(result?.data)
      ? result.data.length
      : result && result.data && typeof result.data.length === 'number'
      ? result.data.length
      : Array.isArray(result?.items)
      ? result.items.length
      : Array.isArray(result?.tasks)
      ? result.tasks.length
      : 0

    const baseMessage = result && result.message ? String(result.message) : ''
    const summarizedMessage =
      dataCount > 0 ? `${baseMessage ? baseMessage + '；' : ''}共 ${dataCount} 条数据` : baseMessage

    // 构建详情（仅非查询类工具）
    const operationType = inferOperationTypeFromTool(toolName, result)
    const details = buildDetails(operationType, toolName, result, parameters)

    const summarizedResult = {
      message: summarizedMessage,
      executionTime: result?.executionTime || null,
      dataCount: dataCount,
      // 可根据需要保留轻量级状态码信息
      errCode: result && result.errCode !== undefined ? result.errCode : 0,
      ...(details ? { details } : {}),
    }

    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_COMPLETE, sessionId, {
        toolName: toolName,
        result: summarizedResult,
        success: true,
        toolCallId: toolCall.id,
      })
    )

    return result
  } catch (error) {
    // 📝 记录工具调用错误出参
    console.log(`[工具调用] ${toolName} - 出参(错误):`, {
      sessionId,
      toolName,
      success: false,
      error: error.message,
      toolCallId: toolCall?.id,
    })

    // 推送工具执行失败消息
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_ERROR, sessionId, {
        toolName: toolName,
        error: error.message,
        success: false,
      })
    )

    throw error
  }
}

/**
 * 继续对话 - 将工具调用结果传回模型
 * @param {Array} originalMessages - 原始消息数组
 * @param {Array} toolCalls - 工具调用数组
 * @param {Array} toolResults - 工具执行结果数组
 * @param {object} sseChannel - SSE通道
 * @param {string} sessionId - 会话ID
 * @param {object} options - 选项配置
 */
async function continueConversationWithToolResults(
  originalMessages,
  toolCalls,
  toolResults,
  sseChannel,
  sessionId,
  options = {}
) {
  const openai = new OpenAI(doubaoParams)

  // 构建包含工具调用和结果的完整消息历史
  const messagesWithToolResults = [
    ...originalMessages,
    // 添加助手的工具调用消息
    {
      role: 'assistant',
      content: null,
      tool_calls: toolCalls,
    },
    // 添加工具执行结果消息
    ...toolResults.map(({ toolCall, result }) => ({
      role: 'tool',
      tool_call_id: toolCall.id,
      content: JSON.stringify(result),
    })),
  ]

  // 推送工具结果处理开始消息
  await sseChannel.write(
    createSSEMessage(SSE_MESSAGE_TYPES.TOOL_RESULT_PROCESSING, sessionId, {
      message: '正在基于工具执行结果生成回复...',
    })
  )

  try {
    // 轮次与时间控制
    const maxRounds = options?.maxRounds ?? 5
    const startedAt = options?.startedAt ?? Date.now()
    const nextOptions = {
      ...options,
      round: (options?.round ?? 0) + 1,
      maxRounds,
      startedAt,
    }

    // 超过最大轮次则停止
    if (nextOptions.round > maxRounds) {
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
          content: `已达到最大工具调用轮次（${maxRounds}），如需继续请确认或再发指令。`,
          isComplete: true,
        })
      )
      return
    }

    // 发起后续对话（允许再次工具调用）
    const followUpResponse = await openai.chat.completions.create({
      model: 'doubao-seed-1-6-flash-250715',
      messages: messagesWithToolResults,
      tools: FUNCTION_TOOLS,
      tool_choice: 'auto',
      stream: true,
      timeout: 60000,
      thinking: { type: 'disabled' },
      // 传入取消信号（如有）
      ...(options && options.abortController ? { signal: options.abortController.signal } : {}),
    })

    // 将后续流交回统一处理（支持多轮）
    // 注意：这里需要动态引入以避免循环依赖
    const { handleStreamResponse } = require('./stream-processor.js')
    return await handleStreamResponse(followUpResponse, sseChannel, sessionId, messagesWithToolResults, {
      ...nextOptions,
      onCanceled: options && options.onCanceled,
      abortController: options && options.abortController,
      isCanceled: options && options.isCanceled,
    })
  } catch (error) {
    // 📝 记录工具结果处理错误
    console.log('[工具结果处理] 失败:', {
      sessionId,
      error: error.message,
      round: options?.round || 0,
      toolCallsCount: toolCalls?.length || 0,
    })

    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_RESULT_ERROR, sessionId, {
        error: '基于工具结果生成回复失败',
        details: error.message,
      })
    )
  }
}

module.exports = {
  executeToolCall,
  continueConversationWithToolResults,
}

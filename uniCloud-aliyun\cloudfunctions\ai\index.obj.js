const { OpenAI } = require('openai')
const {
  FUNCTION_TOOLS,
  doubaoParams,
  SSE_MESSAGE_TYPES,
  createSSEMessage,
  generateSessionId,
} = require('./modules/config.js')
const { upsertSession, markSession, createCancelPoller } = require('./modules/session-manager.js')
const { handleStreamResponse } = require('./modules/stream-processor.js')
const db = uniCloud.database()

module.exports = {
  async chatStreamSSE({ channel, message, messages: history_records }) {
    const sessionId = generateSessionId()

    // 参数验证
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 推送开始处理消息
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.PROCESSING_START, sessionId, {
          message: '开始处理您的请求...',
        })
      )

      // 初始化会话记录
      await upsertSession(db, sessionId, { status: 'running' })

      // 初始化 AI 客户端
      const openai = new OpenAI(doubaoParams)

      // 🎯 获取当前真实时间信息（抽离为通用上下文）
      const { buildTimeContext } = require('./modules/time-context')
      const { timeInfo, raw } = buildTimeContext()
      const {
        tomorrow,
        yesterday,
        dayAfterTomorrow,
        dayBeforeYesterday,
        thisSaturday,
        thisSunday,
        nextMonday,
        lastDayOfMonth,
        firstDayOfNextMonth,
        lastDayOfNextMonth,
      } = raw

      // 构建消息数组 - 正确处理历史消息格式
      const { buildSystemPrompt } = require('./modules/prompts/prompt')
      const systemContent = buildSystemPrompt({
        timeInfo,
        dates: {
          tomorrow: tomorrow.toLocaleDateString('zh-CN'),
          yesterday: yesterday.toLocaleDateString('zh-CN'),
          dayAfterTomorrow: dayAfterTomorrow.toLocaleDateString('zh-CN'),
          dayBeforeYesterday: dayBeforeYesterday.toLocaleDateString('zh-CN'),
          thisSaturday: thisSaturday.toLocaleDateString('zh-CN'),
          thisSunday: thisSunday.toLocaleDateString('zh-CN'),
          nextMonday: nextMonday.toLocaleDateString('zh-CN'),
          lastDayOfMonth: lastDayOfMonth.toLocaleDateString('zh-CN'),
          firstDayOfNextMonth: firstDayOfNextMonth.toLocaleDateString('zh-CN'),
          lastDayOfNextMonth: lastDayOfNextMonth.toLocaleDateString('zh-CN'),
        },
      })

      const messages = [
        {
          role: 'system',
          content: systemContent,
        },
        // 正确处理历史消息，保留工具调用相关信息
        ...history_records.map((msg) => ({
          role: msg.role,
          content: msg.content,
          // 保留工具调用相关信息
          ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
          ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
        })),
        {
          role: 'user',
          content: message,
        },
      ]

      // 创建流式响应
      const abortController = new AbortController()
      // 启动后台轮询：每 1s 查询一次取消标记，命中后立即中断并主动结束
      let canceledFlag = false
      let endSent = false

      const cancelPoller = createCancelPoller(db, sessionId, async () => {
        canceledFlag = true
        // 命中取消：立即中止上游并尝试主动结束，避免等待下一个 chunk
        try {
          abortController.abort()
        } catch (_) {}
        if (!endSent) {
          try {
            await sseChannel.end(
              createSSEMessage(SSE_MESSAGE_TYPES.SESSION_END, sessionId, {
                message: '用户已取消',
                reason: 'canceled',
              })
            )
            endSent = true
          } catch (_) {}
        }
      })
      const streamResponse = await openai.chat.completions.create({
        model: 'doubao-seed-1-6-flash-250715',
        messages: messages,
        tools: FUNCTION_TOOLS,
        tool_choice: 'auto',
        stream: true,
        timeout: 60000,
        thinking: { type: 'disabled' },
        signal: abortController.signal,
      })

      // 处理流式响应 - 传入原始消息用于工具调用后续处理
      await handleStreamResponse(streamResponse, sseChannel, sessionId, messages, {
        round: 0,
        maxRounds: 5,
        startedAt: Date.now(),
        abortController,
        isCanceled: () => canceledFlag,
        onCanceled: () => {
          try {
            abortController.abort()
          } catch (_) {}
        },
      })

      // 推送会话结束消息（仅未取消且未主动发送过 end 时，发送 completed）
      if (!canceledFlag && !endSent) {
        try {
          await sseChannel.end(
            createSSEMessage(SSE_MESSAGE_TYPES.SESSION_END, sessionId, {
              message: '处理完成',
              reason: 'completed',
            })
          )
        } catch (_) {}
        try {
          await markSession(db, sessionId, { status: 'completed' })
        } catch (_) {}
      }

      // 停止轮询器
      cancelPoller.stop()

      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'function_calling_complete',
          sessionId: sessionId,
        },
      }
    } catch (error) {
      // 移除调试日志，仅保留必要的错误日志
      console.error('chatStreamSSE 异常：', error.message, { sessionId })

      // 尝试推送错误消息
      try {
        const sseChannel = uniCloud.deserializeSSEChannel(channel)
        await sseChannel.end(
          createSSEMessage(SSE_MESSAGE_TYPES.ERROR, sessionId, {
            error: error.message,
            timestamp: new Date().toISOString(),
          })
        )
      } catch (channelError) {
        console.error('SSE 推送错误：', channelError)
      }

      await markSession(db, sessionId, { status: 'failed', error: error.message })

      // 停止轮询器
      if (cancelPoller) {
        cancelPoller.stop()
      }

      return {
        errCode: 'SYSTEM_ERROR',
        errMsg: error.message || '系统处理失败',
        data: {
          type: 'system_error',
          sessionId: sessionId,
        },
      }
    }
  },
  /**
   * 取消当前会话
   */
  async cancelChat({ sessionId }) {
    if (!sessionId) {
      return { errCode: 'PARAM_IS_NULL', errMsg: 'sessionId 不能为空' }
    }
    try {
      await markSession(db, sessionId, { canceled: true, status: 'canceled', canceledAt: new Date().toISOString() })
      return { errCode: 0, errMsg: 'success' }
    } catch (e) {
      return { errCode: 'SYSTEM_ERROR', errMsg: e.message || '取消失败' }
    }
  },
}

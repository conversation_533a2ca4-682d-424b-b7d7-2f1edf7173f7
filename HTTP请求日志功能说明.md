# HTTP请求日志功能说明

## 功能概述

已在 `uniCloud-aliyun/cloudfunctions/ai/modules/todo/auth.js` 文件的 `_request` 方法中添加了完整的HTTP请求日志记录功能，用于调试和监控所有HTTP接口调用。

## 新增的日志记录

### 1. HTTP请求入参日志

**位置**: `_request` 方法开始处
**日志标识**: `[HTTP请求] {method} {url} - 入参:`

**记录信息**:
- HTTP方法 (GET/POST/PUT/DELETE等)
- 请求URL (相对路径和完整URL)
- 请求头信息 (隐藏敏感信息)
- 请求体数据 (格式化JSON)
- 请求发起时间
- 超时设置
- 数据大小统计

**日志示例**:
```javascript
[HTTP请求] POST /api/v2/task - 入参: {
  method: 'POST',
  url: '/api/v2/task',
  fullUrl: 'https://api.dida365.com/api/v2/task',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': '[已设置]',
    'User-Agent': 'TodoTool/1.0'
  },
  data: {
    "title": "新任务",
    "content": "任务描述",
    "priority": 1
  },
  hasData: true,
  dataSize: 156,
  requestStartTime: '2023-12-21T03:30:56.789Z',
  timeout: 30000
}
```

### 2. 任务相关请求特别日志

**位置**: 检测到任务相关请求时
**日志标识**: `[HTTP请求] 🎯 任务相关请求详情:`

**触发条件**:
- URL包含 `/task`
- 请求数据包含 `title` 字段

**记录信息**:
- 请求类型判断 (创建/更新/查询)
- 任务标题
- 完整请求数据
- 时间戳

**日志示例**:
```javascript
[HTTP请求] 🎯 任务相关请求详情: {
  method: 'POST',
  endpoint: '/api/v2/task',
  fullUrl: 'https://api.dida365.com/api/v2/task',
  isTaskCreation: true,
  isTaskUpdate: false,
  isTaskQuery: false,
  requestData: {
    "title": "新任务",
    "content": "任务描述",
    "priority": 1
  },
  hasTitle: true,
  taskTitle: "新任务",
  timestamp: '2023-12-21T03:30:56.789Z'
}
```

### 3. HTTP响应成功日志

**位置**: 请求成功后
**日志标识**: `[HTTP请求] {method} {url} - 响应:`

**记录信息**:
- HTTP状态码和状态文本
- 执行时间
- 响应数据大小
- 请求结束时间
- 成功状态

**日志示例**:
```javascript
[HTTP请求] POST /api/v2/task - 响应: {
  method: 'POST',
  url: '/api/v2/task',
  statusCode: 200,
  statusText: 'OK',
  executionTime: '1250ms',
  responseSize: 2048,
  hasResponseData: true,
  requestEndTime: '2023-12-21T03:30:58.039Z',
  success: true
}
```

### 4. 任务相关响应特别日志

**位置**: 任务相关请求成功后
**日志标识**: `[HTTP请求] 🎯 任务相关响应详情:`

**记录信息**:
- 完整响应数据 (格式化JSON)
- 执行时间
- 成功状态

**日志示例**:
```javascript
[HTTP请求] 🎯 任务相关响应详情: {
  method: 'POST',
  endpoint: '/api/v2/task',
  statusCode: 200,
  executionTime: '1250ms',
  responseData: {
    "id": "task_123456",
    "title": "新任务",
    "status": 0,
    "createdTime": "2023-12-21T03:30:58.000Z"
  },
  isSuccess: true,
  timestamp: '2023-12-21T03:30:58.039Z'
}
```

### 5. HTTP请求错误日志

**位置**: 请求失败时
**日志标识**: `[HTTP请求] {method} {url} - 错误:`

**记录信息**:
- 错误名称、消息、代码
- 执行时间
- 请求数据
- 错误发生时间

**日志示例**:
```javascript
[HTTP请求] POST /api/v2/task - 错误: {
  method: 'POST',
  url: '/api/v2/task',
  fullUrl: 'https://api.dida365.com/api/v2/task',
  errorName: 'NetworkError',
  errorMessage: 'Request timeout',
  errorCode: 'TIMEOUT',
  executionTime: '30000ms',
  requestData: {
    "title": "新任务"
  },
  requestEndTime: '2023-12-21T04:01:26.789Z',
  success: false
}
```

### 6. 任务相关错误特别日志

**位置**: 任务相关请求失败时
**日志标识**: `[HTTP请求] 🎯 任务相关请求失败:`

**记录信息**:
- 详细错误信息
- 完整请求数据
- 错误堆栈

## 安全特性

### 1. 敏感信息保护
- Authorization头显示为 `[已设置]` 或 `[未设置]`
- 只显示关键的请求头字段
- 不记录完整的认证token

### 2. 数据大小控制
- 记录请求和响应数据的大小
- 对大数据进行格式化显示
- 避免日志过大影响性能

## 使用场景

### 1. 接口调试
- 查看完整的HTTP请求参数
- 验证请求头设置是否正确
- 检查请求体数据格式

### 2. 性能监控
- 监控接口响应时间
- 识别慢接口
- 分析网络请求性能

### 3. 错误排查
- 快速定位HTTP请求失败原因
- 查看详细的错误信息
- 分析请求参数是否正确

### 4. 任务操作追踪
- 特别关注任务创建、更新操作
- 记录任务相关的详细信息
- 便于排查任务操作问题

## 日志查看方式

### 1. 云函数日志
在uniCloud控制台的云函数日志中查看：
```
[HTTP请求] POST /api/v2/task - 入参: { ... }
[HTTP请求] 🎯 任务相关请求详情: { ... }
[HTTP请求] POST /api/v2/task - 响应: { ... }
[HTTP请求] 🎯 任务相关响应详情: { ... }
```

### 2. 本地调试
在本地开发时，通过console输出查看详细的HTTP请求信息。

## 性能影响

### 1. 最小化影响
- 只在必要时记录详细信息
- 使用异步日志记录
- 避免阻塞主要业务流程

### 2. 可控制性
- 可以通过修改日志级别控制输出
- 生产环境可以选择性关闭详细日志

## 总结

通过添加完整的HTTP请求日志记录功能，现在可以：

1. **完整追踪** - 从请求发起到响应接收的完整过程
2. **详细信息** - 包含所有必要的调试信息
3. **特别关注** - 对任务相关操作进行特别记录
4. **安全保护** - 隐藏敏感信息，保护系统安全
5. **易于调试** - 清晰的日志格式，便于问题定位

这些日志将大大提升HTTP接口调用问题的调试效率。

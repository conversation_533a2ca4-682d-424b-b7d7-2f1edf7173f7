/**
 * 工具执行详情构建模块
 * 包含工具执行结果的详情构建和格式化功能
 */

// 工具执行详情构建配置
const DETAILS_LIMITS = {
  MAX_LIST: 20,
  STRING_MAX_LEN: 120,
}

/**
 * 从工具名称和结果推断操作类型
 * @param {string} toolName - 工具名称
 * @param {object} result - 工具执行结果
 * @returns {string} 操作类型: 'create' | 'update' | 'delete' | 'query'
 */
function inferOperationTypeFromTool(toolName, result) {
  if (result && typeof result.operationType === 'string') {
    const t = String(result.operationType).toLowerCase()
    if (t === 'create' || t === 'update' || t === 'delete' || t === 'query') return t
  }
  const name = (toolName || '').toLowerCase()
  if (name.startsWith('get')) return 'query'
  if (name.startsWith('create')) return 'create'
  if (name.startsWith('update') || name.includes('rename') || name.includes('merge')) return 'update'
  if (name.startsWith('delete')) return 'delete'
  // 兜底：无匹配视为查询
  return 'query'
}

/**
 * 截断字符串到指定长度
 * @param {any} s - 待截断的值
 * @returns {any} 截断后的字符串或原值
 */
function truncateString(s) {
  if (typeof s !== 'string') return s
  return s.length > DETAILS_LIMITS.STRING_MAX_LEN ? s.slice(0, DETAILS_LIMITS.STRING_MAX_LEN) + '…' : s
}

/**
 * 确保返回数组
 * @param {any} v - 待处理的值
 * @returns {Array} 数组形式的结果
 */
function ensureArray(v) {
  if (Array.isArray(v)) return v
  if (v === null || v === undefined) return []
  return [v]
}

/**
 * 从对象中提取ID
 * @param {object} obj - 待提取ID的对象
 * @returns {string|null} 提取到的ID或null
 */
function pickId(obj) {
  if (!obj || typeof obj !== 'object') return null
  return obj.id || obj._id || obj.taskId || obj.projectId || null
}

/**
 * 构建工具执行详情
 * @param {string} operationType - 操作类型
 * @param {string} toolName - 工具名称
 * @param {object} result - 工具执行结果
 * @param {object} parameters - 工具执行参数
 * @returns {object|null} 构建的详情对象或null
 */
function buildDetails(operationType, toolName, result, parameters) {
  try {
    // 仅对非查询类返回详情
    if (!operationType || operationType === 'query') return null

    const data = result && result.data
    const items = ensureArray(data)

    let truncated = false
    let list = items
    if (list.length > DETAILS_LIMITS.MAX_LIST) {
      truncated = true
      list = list.slice(0, DETAILS_LIMITS.MAX_LIST)
    }

    const ids = []
    for (const it of list) {
      const id = pickId(it)
      if (id) ids.push(String(id))
      if (ids.length >= DETAILS_LIMITS.MAX_LIST) break
    }

    const base = {
      operationType,
      summary: String(result?.message || ''),
      rowsAffected: 0,
      affectedIds: ids,
      truncated,
    }

    if (operationType === 'create') {
      const createdIds = ids.length > 0 ? ids : parameters && parameters.id ? [String(parameters.id)] : []
      const preview = list
        .map((it) => ({
          _id: pickId(it) || null,
          title: (it && (it.title || it.name || it.content)) || undefined,
          startDate: (it && it.startDate) || undefined,
          dueDate: (it && it.dueDate) || undefined,
        }))
        .filter(Boolean)
        .slice(0, DETAILS_LIMITS.MAX_LIST)
      return {
        ...base,
        createdIds,
        createdCount: createdIds.length || (items.length > 0 ? items.length : 0),
        rowsAffected: createdIds.length || (items.length > 0 ? items.length : 0),
        preview,
      }
    }

    if (operationType === 'update') {
      // 尝试解析更新对象 ID
      const primaryId =
        ids[0] || (parameters && (parameters.taskId || parameters.projectIdOrName || parameters.tagIdOrName)) || null
      const changedFields = []
      const diff = {}
      if (parameters && typeof parameters === 'object') {
        for (const [k, v] of Object.entries(parameters)) {
          // 跳过定位字段与空值
          if (k === 'taskId' || k === 'projectIdOrName' || k === 'tagIdOrName') continue
          if (v === undefined) continue
          changedFields.push(k)
          diff[k] = { before: null, after: truncateString(typeof v === 'string' ? v : JSON.stringify(v)) }
        }
      }
      const changeEntry = {
        _id: primaryId ? String(primaryId) : undefined,
        changedFields,
        diff,
        title: (list[0] && (list[0].title || list[0].name)) || undefined,
      }
      const updatedIds = primaryId ? [String(primaryId)] : ids
      return {
        ...base,
        updatedIds,
        updatedCount: updatedIds.length || 1,
        rowsAffected: updatedIds.length || 1,
        changes: [changeEntry].slice(0, DETAILS_LIMITS.MAX_LIST),
      }
    }

    if (operationType === 'delete') {
      const deletedIds =
        ids.length > 0
          ? ids
          : parameters && (parameters.taskId || parameters.projectIdOrName || parameters.tagIdOrName)
          ? [String(parameters.taskId || parameters.projectIdOrName || parameters.tagIdOrName)]
          : []
      const titles = list
        .map((it) => (it && (it.title || it.name)) || null)
        .filter(Boolean)
        .slice(0, DETAILS_LIMITS.MAX_LIST)
      return {
        ...base,
        deletedIds,
        deletedCount: deletedIds.length || 1,
        rowsAffected: deletedIds.length || 1,
        titles,
      }
    }

    // 未知类型兜底：不返回详情
    return null
  } catch (e) {
    console.warn('[buildDetails] 构建详情失败，忽略', { error: e && e.message })
    return null
  }
}

module.exports = {
  DETAILS_LIMITS,
  inferOperationTypeFromTool,
  truncateString,
  ensureArray,
  pickId,
  buildDetails,
}
